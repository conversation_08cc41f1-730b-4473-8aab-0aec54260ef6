// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/model"
	"context"
)

type (
	IDSH interface {
		GetResources(ctx context.Context, tenantID string, serviceID string) (records []*model.ResourceRecord, err error)
		GetChatMessages(ctx context.Context, tenantID string, serviceID string, userID string, channel string) (messages []string, err error)
		InsertNewChatMessage(ctx context.Context, message *model.GenericMessage) (err error)
		GetSystemInstruction(ctx context.Context, tenantID string) (sysInstruction *model.SystemInstruction, err error)
		GetLLMParams(ctx context.Context, tenantID string) (llmParams *model.LLMParams, err error)
	}
)

var (
	localDSH IDSH
)

func DSH() IDSH {
	if localDSH == nil {
		panic("implement not found for interface IDSH, forgot register?")
	}
	return localDSH
}

func RegisterDSH(i IDSH) {
	localDSH = i
}
