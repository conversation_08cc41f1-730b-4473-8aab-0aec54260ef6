package cmd

import (
	"brainHub/internal/consts"
	"brainHub/internal/controller/brain"
	"brainHub/internal/controller/embedding"
	"brainHub/internal/controller/omnichannel"
	"brainHub/internal/logic/messageQ"
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			if err := messageQ.SetupMessageQ(ctx); err != nil {
				g.Log().Error(ctx, err)
				panic(err)
			}

			s := g.Server(consts.ServiceName)
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(MiddleHandler, ghttp.MiddlewareHandlerResponse)
				group.Bind(
					omnichannel.NewV1(),
					brain.NewV1(),
					embedding.NewV1(),
				)
			})
			s.Run()
			return nil
		},
	}
)

func MiddleHandler(r *ghttp.Request) {
	// Interception and processing of all requests are processed in middleware
	ctx := r.GetCtx()
	r.Response.CORSDefault()
	if gjson.Valid(r.GetBodyString()) {
		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Request-Uri: %s Body:%s", r.RequestURI, r.GetBodyString())
	} else {
		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Request-Uri: %s ", r.RequestURI)

	}
	r.Middleware.Next()

	if gjson.Valid(r.Response.BufferString()) {

		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Response-%s", r.Response.BufferString())
	} else {
		g.Log().Cat(consts.CatalogReqRes).Debug(ctx, "Return response")

	}

}
