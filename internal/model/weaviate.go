package model

import "github.com/weaviate/weaviate/entities/models"

type CreateCollectionInput struct {
	Name               string             `json:"name" v:"required"`
	Properties         []*models.Property `json:"properties" v:"required"`
	VectoredProperties []string           `json:"vectored_properties"`
	EmbeddingModel     string             `json:"embedding_model" v:"required"`
	ResourceName       string             `json:"resource_name" v:"required"`
	Tenants            []string           `json:"tenants"`
	MultiTenancy       bool               `json:"multi_tenancy"`
}
