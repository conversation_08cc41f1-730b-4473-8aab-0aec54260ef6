package consts

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/weaviate/weaviate/entities/models"
	"github.com/weaviate/weaviate/entities/schema"
)

// modelConfig 用于配置 Weaviate 中的模块参数
var moduleConfig = map[string]interface{}{
	"text2vec-openai": map[string]interface{}{
		"skip":                  true,
		"vectorizePropertyName": false,
	},
}

const ChatMessage = `chat_message`

// CollectionPros 用于定义 Weaviate 中的集合属性
var CollectionPros = g.Map{
	ChatMessage: []*models.Property{
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "message",
			ModuleConfig: moduleConfig,
			Tokenization: models.PropertyTokenizationGse,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "service_id",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "user_id",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "role",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "channel",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "message_type",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "display_name",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeDate.String()},
			Name:         "crate_at",
			ModuleConfig: moduleConfig,
		},
	},
}
